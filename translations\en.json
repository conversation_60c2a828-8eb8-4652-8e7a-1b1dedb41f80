{"common": {"submit": "Submit", "name": "Name", "description": "Description", "delete": "Delete", "cancel": "Cancel", "plantName": "Plant name", "disable": "Disable", "enable": "Enable", "confirm": "Confirm", "copy": "Copy", "close": "Close", "update": "Update", "rate": "Rate"}, "pages": {"homepage": {"newUpdateHeader": "New update available"}, "plants": {"add": {"plantNamePlaceholder": "Enter your plant name...", "plantDescriptionPlaceholder": "Enter your plant description...", "uploadPicture": "Upload picture", "submit": "Add plant", "success": "Plant added", "remindWateringLabel": "Remind me of watering"}, "edit": {"editPicture": "Edit picture", "plantNamePlaceholder": "Enter your plant name...", "plantDescriptionPlaceholder": "Enter your plant description...", "submit": "Submit changes", "success": "Plant edited", "plantDeletedSuccess": "Plant deleted", "createdAt": "Created at {{date}}", "deletePlantConfirmation": "Are you sure to delete this plant from your collection?", "remindWateringLabel": "Remind me of watering"}, "import": {"description": "Every plant added to the app by any user has its own unique code. You can ask your friend for their plant code and then import it to your collection here. This way, you will be able to water it with together with them.", "inputLabel": "Plant code", "inputPlaceholder": "Enter plant code...", "success": "Plant imported"}, "history": {"wateringHeader": "Watering history", "imagesHeader": "Images history", "addImageHeader": "Add image", "plantNotWatered": "This plant has not been watered yet.", "plantHasNoImages": "This plant has not any images history yet.", "chooseImage": "Choose image", "addImage": "Add image", "success": "Image added", "imageDeleted": "Image deleted", "deleteImageConfirmation": "Are you sure to delete this image?", "shareModal": {"header": "Share plant", "description": "Let your friend friend add this plant to their collection, so they can water it together with you."}}}, "settings": {"header": "Settings", "notificationsHeader": "Notifications", "notifications": {"manageHeader": "Manage notifications", "manageDescription": "Notifications are {{state}}", "notificationsEnabled": "enabled", "notificationsDisabled": "disabled"}, "app": {"header": "App", "manageTheme": "Change theme", "darkTheme": "Dark", "lightTheme": "Light"}, "contact": "Contact", "reportBug": {"header": "Report bug", "description": "If you encounter any issues with the app or if you have any suggestions, please don't hesitate to reach out to our team. We appreciate your feedback and are always striving to improve our services.", "emailLabel": "Email (optional)", "descriptionLabel": "Description", "descriptionPlaceholder": "When I clicked on my plant...", "success": "Report sent"}, "appVersion": "App version: {{version}}"}}, "errors": {"required": "Required", "general": "Something went wrong", "generalDescription": "Please try again later", "invalidFileType": "Invalid file type", "fieldTooLong": "{{field}} too long. Maximum {{number}} characters", "valueMustBeNumber": "Value must be a number", "minimumValueIs": "Minimum value is: {{value}}", "maximumValueIs": "Maximum value is: {{value}}", "plantAlreadyAdded": "Plant already exists in your collection", "plantNotExists": "Plant not found", "emailOptional": "Must be valid email or empty field", "tooManyBugReports": "Too many bug reports"}, "components": {"basicImageInput": {"takePhoto": "Take photo", "chooseLibrary": "Choose from library"}, "addPlantSuggestion": "Add your first plant here", "plant": {"success": "Plant watered", "wateringCanceled": "Watering canceled"}, "wateringReminderInput": {"every": "Every", "daySingular": "day", "dayPlurar": "days"}, "rateAppModal": {"header": "Are you enjoying the app? Rate it on the store, so other people can see it"}, "noConnectionModal": {"header": "No connection", "description": "Please check your network connection and try again"}, "helpModal": {"slider": "Move slider to mark plant as watered", "history": "Tap plant to view watering and photo history", "edit": "Hold plant to edit its details"}, "errorBoundary": {"header": "Something went wrong", "description": "Please try to restart the app"}}}