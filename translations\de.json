{"common": {"submit": "<PERSON><PERSON><PERSON><PERSON>", "name": "Name", "description": "Beschreibung", "delete": "Löschen", "cancel": "Abbrechen", "plantName": "Pflanzenname", "disable": "Deaktivieren", "enable": "Aktivieren", "confirm": "Bestätigen", "copy": "<PERSON><PERSON><PERSON>", "close": "Schließen", "update": "Aktualisieren", "rate": "Bewerten"}, "pages": {"homepage": {"newUpdateHeader": "Neues Update verfügbar"}, "plants": {"add": {"plantNamePlaceholder": "Geben Sie Ihren Pflanzennamen ein...", "plantDescriptionPlaceholder": "Geben Sie Ihre Pflanzenbeschreibung ein...", "uploadPicture": "Bild hochladen", "submit": "Pflanze hinzufügen", "success": "<PERSON><PERSON>lan<PERSON> hinzugefügt", "remindWateringLabel": "<PERSON><PERSON>n Si<PERSON> mich an<PERSON> G<PERSON>ßen"}, "edit": {"editPicture": "Bild bearbeiten", "plantNamePlaceholder": "Geben Sie Ihren Pflanzennamen ein...", "plantDescriptionPlaceholder": "Geben Sie Ihre Pflanzenbeschreibung ein...", "submit": "Änderungen absenden", "success": "Pflanze bearbei<PERSON>t", "plantDeletedSuccess": "Pflan<PERSON>", "createdAt": "Erstellt am {{date}}", "deletePlantConfirmation": "Sind <PERSON> sicher, dass Sie diese Pflanze aus Ihrer Sammlung löschen möchten?", "remindWateringLabel": "<PERSON><PERSON>n Si<PERSON> mich an<PERSON> G<PERSON>ßen"}, "import": {"description": "<PERSON><PERSON> von einem Benutzer zur App hinzugefügte Pflanze hat ihren eigenen eindeutigen Code. Sie können Ihren Freund nach ihrem Pflanzencode fragen und ihn dann hier in Ihre Sammlung importieren. Auf diese Weise können Sie es zusammen mit ihnen bewässern.", "inputLabel": "Pflanzencode", "inputPlaceholder": "Pflanzencode eingeben...", "success": "Pflanze importiert"}, "history": {"wateringHeader": "Bewässerungsverlauf", "imagesHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addImageHeader": "Bild hinzufügen", "plantNotWatered": "Diese Pflanze wurde noch nicht bewässert.", "plantHasNoImages": "Diese Pflanze hat noch keine Bildhistorie.", "chooseImage": "Bild auswählen", "addImage": "Bild hinzufügen", "success": "<PERSON><PERSON><PERSON>", "imageDeleted": "Bild <PERSON>", "deleteImageConfirmation": "Sind Si<PERSON> sicher, dass Sie dieses Bild löschen möchten?", "shareModal": {"header": "P<PERSON>lanze teilen", "description": "<PERSON>sen Sie Ihren Freund diese Pflanze zu ihrer Sammlung hinzufügen, damit sie zusammen mit Ihnen bewässert werden kann."}}}, "settings": {"header": "Einstellungen", "notificationsHeader": "Benachrichtigungen", "notifications": {"manageHeader": "Benachrichtigungen verwalten", "manageDescription": "Benachrichtigungen sind {{state}}", "notificationsEnabled": "aktiviert", "notificationsDisabled": "<PERSON>ak<PERSON><PERSON><PERSON>"}, "app": {"header": "App", "manageTheme": "<PERSON><PERSON>", "darkTheme": "<PERSON><PERSON><PERSON>", "lightTheme": "Hell"}, "contact": "Kontakt", "reportBug": {"header": "<PERSON><PERSON> melden", "description": "Wenn Sie Probleme mit der App haben oder Verbesserungsvorschläge haben, z<PERSON><PERSON><PERSON>, sich an unser Team zu wenden. Wir schätzen Ihre Meinung und sind immer bestrebt, unsere Dienstleistungen zu verbessern.", "emailLabel": "E-Mail (optional)", "descriptionLabel": "Fehlerbeschreibung", "descriptionPlaceholder": "<PERSON><PERSON> einer Pflanze...", "success": "Meldung gesendet"}, "appVersion": "App-Version: {{version}}"}}, "errors": {"required": "<PERSON><PERSON> ist erford<PERSON>lich", "general": "Etwas ist schiefgelaufen", "generalDescription": "Bitte versuchen Sie es später erneut", "invalidFileType": "Ungültiger Dateityp", "fieldTooLong": "{{field}} darf höchstens {{number}} Buchstaben enthalten", "valueMustBeNumber": "Der Wert muss eine Zahl sein", "minimumValueIs": "Der minimale Wert beträgt: {{value}}", "maximumValueIs": "Der maximale Wert beträgt: {{value}}", "plantAlreadyAdded": "Die Pflanze befindet sich bereits in Ihrer Sammlung", "plantNotExists": "Die Pflanze existiert nicht", "emailOptional": "Geben Sie eine gültige E-Mail-Adresse an oder lassen Si<PERSON> das Feld leer", "tooManyBugReports": "Zu viele Meldungen"}, "components": {"basicImageInput": {"takePhoto": "Foto aufnehmen", "chooseLibrary": "Aus Galerie auswählen"}, "addPlantSuggestion": "Fügen Sie Ihre erste Pflanze hinzu", "plant": {"success": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "wateringCanceled": "Bewässerung abgebrochen"}, "wateringReminderInput": {"every": "<PERSON><PERSON>", "daySingular": "Tag", "dayPlurar": "Tage"}, "rateAppModal": {"header": "Gefä<PERSON>t Ihnen die App? Bitte bewerten Sie sie im Store, damit andere sie sehen können"}, "noConnectionModal": {"header": "<PERSON><PERSON>", "description": "Überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut"}, "helpModal": {"slider": "Sc<PERSON>eb<PERSON> Sie den Regler, um die Pflanze als gegossen zu markieren", "history": "<PERSON><PERSON><PERSON> auf die Pflanze, um die Bewässerungs- und Fotohistorie anzuzeigen", "edit": "Halten Si<PERSON> die Pflanze gedrückt, um ihre Details zu bearbeiten"}, "errorBoundary": {"header": "Etwas ist schief gelaufen", "description": "Starten Sie die Anwendung neu und versuchen Sie es erneut"}}}