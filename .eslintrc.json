{"env": {"es2021": true, "node": true}, "extends": ["@react-native-community", "plugin:react/recommended", "airbnb-typescript", "airbnb/hooks", "xo", "prettier"], "overrides": [{"extends": ["xo-typescript"], "files": ["*.ts", "*.tsx"]}], "parserOptions": {"project": ["./tsconfig.json"], "ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"tsx": true}}, "plugins": ["react", "react-native"], "rules": {}}