{"extends": "expo/tsconfig.base", "compilerOptions": {"jsx": "react", "strict": true, "baseUrl": "./", "paths": {"assets/*": ["assets/*"], "components/*": ["components/*"], "config/*": ["config/*"], "interfaces/*": ["interfaces/*"], "schemas/*": ["schemas/*"], "screens/*": ["screens/*"], "store/*": ["store/*"], "styles/*": ["styles/*"], "utils/*": ["utils/*"], "enums/*": ["enums/*"], "providers/*": ["providers/*"], "services/*": ["services/*"], "hooks/*": ["hooks/*"], "modals/*": ["modals/*"]}}}