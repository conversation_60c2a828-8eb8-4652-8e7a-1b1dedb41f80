module.exports = function (api) {
    api.cache(true);
    return {
        presets: [
            [
                "babel-preset-expo",
                {
                    unstable_transformImportMeta: true
                }
            ]
        ],
        plugins: [
            [
                "module-resolver",
                {
                    alias: {
                        assets: "./assets",
                        components: "./components",
                        config: "./config",
                        interfaces: "./interfaces",
                        schemas: "./schemas",
                        screens: "./screens",
                        store: "./store",
                        styles: "./styles",
                        utils: "./utils",
                        enums: "./enums",
                        providers: "./providers",
                        services: "./services",
                        hooks: "./hooks",
                        modals: "./modals"
                    },
                },
            ],
            "react-native-reanimated/plugin",
        ],
    };
};
