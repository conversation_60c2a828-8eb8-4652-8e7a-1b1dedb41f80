{"name": "my-plants", "private": true, "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "start:tunnel": "expo start --tunnel", "deploy:build": "eas build", "deploy:publish": "eas submit", "test": "jest", "lint": "tsc --noEmit && eslint --ext .js,.jsx,.ts,.tsx ./", "prettier:write": "npx prettier --write **/*.{js,jsx,ts,tsx,json} && npx prettier --write *.{js,jsx,ts,tsx,json}", "eas-build-pre-install": "echo $GOOGLE_SERVICES_BASE64 | base64 --decode > keys/google-services.json"}, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|expo-app-loading|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg|moti|@miblanchard/react-native-slider)"]}, "dependencies": {"@expo-google-fonts/akaya-kanadaka": "^0.2.2", "@expo-google-fonts/dev": "^0.2.2", "@expo-google-fonts/inter": "^0.2.2", "@expo/vector-icons": "^14.1.0", "@miblanchard/react-native-slider": "^2.3.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "11.4.1", "@react-navigation/native": "^6.0.10", "@react-navigation/native-stack": "^6.6.2", "@sentry/react-native": "~6.14.0", "@types/styled-components": "^5.1.25", "@types/styled-components-react-native": "^5.1.3", "axios": "^0.27.2", "dayjs": "^1.11.4", "dotenv": "^16.0.2", "eslint-plugin-simple-import-sort": "^8.0.0", "expo": "^53.0.0", "expo-application": "~6.1.4", "expo-checkbox": "~4.1.4", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-device": "~7.1.4", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-notifications": "~0.31.2", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-updates": "~0.28.13", "formik": "^2.2.9", "i18n-js": "^3.9.2", "jest": "^29.2.1", "jest-expo": "~53.0.0", "moti": "^0.18.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-get-random-values": "~1.11.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-toast-message": "^2.1.6", "react-native-version-check-expo": "^3.4.7", "react-native-web": "^0.20.0", "sentry-expo": "~7.0.0", "styled-components": "^5.3.5", "ts-jest": "^27.1.5", "uuid": "^9.0.0", "yup": "^0.32.11", "zustand": "^4.1.2"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/runtime": "^7.27.4", "@expo/webpack-config": "^19.0.0", "@react-native-community/eslint-config": "^3.2.0", "@types/jest": "^29.2.3", "@types/react": "~18.2.14", "@types/react-native-version-check": "^3.4.5", "@types/react-redux": "^7.1.24", "@types/react-test-renderer": "^18.0.0", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.46.1", "@typescript-eslint/parser": "^5.46.1", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.29.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-config-standard-with-typescript": "^24.0.0", "eslint-config-xo": "^0.43.1", "eslint-config-xo-typescript": "^0.55.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-n": "^15.6.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.0.0", "prettier": "^2.8.1", "react-test-renderer": "^18.0.0", "typescript": "^5.1.3"}}